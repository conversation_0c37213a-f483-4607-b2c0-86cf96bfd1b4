import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function Services() {
  const [selectedService, setSelectedService] = useState(null);
  const navigate = useNavigate();

  const services = [
    {
      id: 1,
      name: "Trekking and Jeep",
      price: 150000,
      rating: 5,
      reviews: 175,
      description: "Enjoy the atmosphere of trekking and riding a jeep through breathtaking landscapes, where every turn reveals stunning natural beauty. Feel the fresh mountain breeze, the thrill of adventure, and the peaceful vibe of untouched nature that surrounds you. This unforgettable journey offers more than just excitement; it creates lasting memories and a deep connection with the great outdoors.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🚗", name: "Jeep Car" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ]
    },
    {
      id: 2,
      name: "Swing and Nest",
      price: 150000,
      rating: 5,
      reviews: 150,
      description: "Experience the ultimate thrill with our swing and nest adventure, offering breathtaking panoramic views and unforgettable moments suspended above nature's beauty.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🪺", name: "Nest Experience" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ]
    },
    {
      id: 3,
      name: "Rafting",
      price: 150000,
      rating: 5,
      reviews: 200,
      description: "Navigate through exciting rapids and pristine waters in our professional rafting adventure, perfect for thrill-seekers and nature lovers alike.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🚣", name: "Rafting Equipment" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ]
    },
    {
      id: 4,
      name: "ATV Ride",
      price: 150000,
      rating: 5,
      reviews: 180,
      description: "Explore rugged terrains and hidden trails with our ATV riding experience, combining adventure with stunning natural scenery.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🏍️", name: "ATV Vehicle" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ]
    }
  ];

  const handleBookNow = (service) => {
    navigate('/booking', { state: { selectedService: service } });
  };

  const handleServiceClick = (service) => {
    setSelectedService(service);
  };

  const handleCloseDetail = () => {
    setSelectedService(null);
  };

  if (selectedService) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-6 py-8">
          <button
            onClick={handleCloseDetail}
            className="mb-6 text-ubud-dark-green hover:text-ubud-light-green font-medium"
          >
            ← Back to Services
          </button>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="md:flex">
              <div className="md:w-1/2">
                <img
                  src={selectedService.image}
                  alt={selectedService.name}
                  className="w-full h-64 md:h-full object-cover"
                />
              </div>
              <div className="md:w-1/2 p-8">
                <h1 className="text-3xl font-bold text-ubud-dark-green mb-4">
                  {selectedService.name}
                </h1>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {selectedService.description}
                </p>
                <div className="flex items-center mb-6">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(selectedService.rating)].map((_, i) => (
                      <span key={i}>⭐</span>
                    ))}
                  </div>
                  <span className="text-gray-600">({selectedService.reviews} Reviews)</span>
                </div>
                <button
                  onClick={() => handleBookNow(selectedService)}
                  className="bg-ubud-dark-green text-white px-8 py-3 rounded-lg hover:bg-ubud-light-green transition-colors font-medium"
                >
                  Book Now
                </button>
              </div>
            </div>

            {/* Gallery */}
            <div className="p-8">
              <div className="grid grid-cols-3 gap-4 mb-8">
                {selectedService.gallery.map((img, index) => (
                  <img
                    key={index}
                    src={img}
                    alt={`${selectedService.name} ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                ))}
              </div>

              {/* Facilities */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-4">Facilities</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {selectedService.facilities.map((facility, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-2xl">{facility.icon}</span>
                      <span className="text-gray-700">{facility.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Customer Reviews */}
              <div>
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-6">Customer Review</h3>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-ubud-dark-green text-white p-6 rounded-lg">
                    <div className="text-4xl mb-4">"</div>
                    <p className="mb-4">Good service, cheap, with guides who are very professional in their fields. The experience here is very engaging fun!</p>
                    <div className="flex items-center">
                      <img src="/src/assets/jeep-group.png" alt="Customer" className="w-12 h-12 rounded-full mr-3" />
                      <div>
                        <div className="font-semibold">John Doe</div>
                        <div className="text-sm opacity-75">Tourist</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-ubud-yellow text-ubud-dark-green p-6 rounded-lg">
                    <div className="text-4xl mb-4">"</div>
                    <p className="mb-4">Good service, cheap, with guides who are very professional in their fields. The experience here is very engaging fun!</p>
                    <div className="flex items-center">
                      <img src="/src/assets/jeep-group.png" alt="Customer" className="w-12 h-12 rounded-full mr-3" />
                      <div>
                        <div className="font-semibold">Jane Smith</div>
                        <div className="text-sm opacity-75">Tourist</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-ubud-dark-green text-white p-6 rounded-lg">
                    <div className="text-4xl mb-4">"</div>
                    <p className="mb-4">Good service, cheap, with guides who are very professional in their fields. The experience here is very engaging fun!</p>
                    <div className="flex items-center">
                      <img src="/src/assets/jeep-group.png" alt="Customer" className="w-12 h-12 rounded-full mr-3" />
                      <div>
                        <div className="font-semibold">Mike Johnson</div>
                        <div className="text-sm opacity-75">Tourist</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-12">
        <h1 className="text-4xl font-bold text-ubud-dark-green text-center mb-12">
          Our Services
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
          {services.map((service) => (
            <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <img
                src={service.image}
                alt={service.name}
                className="w-full h-48 object-cover cursor-pointer"
                onClick={() => handleServiceClick(service)}
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-2">
                  {service.name}
                </h3>
                <div className="flex items-center mb-3">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(service.rating)].map((_, i) => (
                      <span key={i}>⭐</span>
                    ))}
                  </div>
                  <span className="text-gray-600 text-sm">({service.reviews} Reviews)</span>
                </div>
                <p className="text-gray-600 mb-4 text-sm line-clamp-3">
                  {service.description}
                </p>
                <div className="text-ubud-dark-green font-bold text-lg mb-4">
                  Rp. {service.price.toLocaleString('id-ID')}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleServiceClick(service)}
                    className="flex-1 bg-gray-100 text-ubud-dark-green py-2 px-4 rounded hover:bg-gray-200 transition-colors font-medium"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => handleBookNow(service)}
                    className="flex-1 bg-ubud-dark-green text-white py-2 px-4 rounded hover:bg-ubud-light-green transition-colors font-medium"
                  >
                    Book Now
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
