import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function Services() {
  const [selectedService, setSelectedService] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const navigate = useNavigate();

  const services = [
    {
      id: 1,
      name: "Trekking and Jeep",
      price: 150000,
      rating: 5,
      reviews: 175,
      description: "Enjoy the atmosphere of trekking and riding a jeep through breathtaking landscapes, where every turn reveals stunning natural beauty. Feel the fresh mountain breeze, the thrill of adventure, and the peaceful vibe of untouched nature that surrounds you. This unforgettable journey offers more than just excitement; it creates lasting memories and a deep connection with the great outdoors.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🚗", name: "Jeep Car" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ],
      customerReviews: [
        {
          id: 1,
          name: "John Doe",
          role: "Tourist",
          review: "Good service, cheap, with guides who are very professional in their fields. The experience here is very engaging fun!",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 2,
          name: "Jane Smith",
          role: "Tourist",
          review: "Amazing adventure! The jeep ride was thrilling and the trekking experience was unforgettable. Highly recommended!",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 3,
          name: "Mike Johnson",
          role: "Tourist",
          review: "Professional guides and excellent service. The views were breathtaking and worth every penny!",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 4,
          name: "Sarah Wilson",
          role: "Tourist",
          review: "Best adventure experience in Bali! The team was very helpful and the journey was amazing.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 5,
          name: "David Brown",
          role: "Tourist",
          review: "Incredible experience with stunning landscapes. The guides were knowledgeable and friendly.",
          avatar: "/src/assets/jeep-group.png"
        }
      ]
    },
    {
      id: 2,
      name: "Swing and Nest",
      price: 150000,
      rating: 5,
      reviews: 150,
      description: "Experience the ultimate thrill with our swing and nest adventure, offering breathtaking panoramic views and unforgettable moments suspended above nature's beauty.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🪺", name: "Nest Experience" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ],
      customerReviews: [
        {
          id: 1,
          name: "Emma Davis",
          role: "Tourist",
          review: "The swing experience was absolutely magical! Perfect for photos and the adrenaline rush was incredible.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 2,
          name: "Alex Chen",
          role: "Tourist",
          review: "Amazing views from the nest! The staff was very professional and made sure we felt safe throughout.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 3,
          name: "Lisa Rodriguez",
          role: "Tourist",
          review: "Best Instagram-worthy spot in Ubud! The swing over the jungle was breathtaking.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 4,
          name: "Tom Anderson",
          role: "Tourist",
          review: "Thrilling experience with stunning panoramic views. Highly recommend for adventure seekers!",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 5,
          name: "Maria Garcia",
          role: "Tourist",
          review: "The nest experience was unique and the swing was so much fun. Great value for money!",
          avatar: "/src/assets/jeep-group.png"
        }
      ]
    },
    {
      id: 3,
      name: "Rafting",
      price: 150000,
      rating: 5,
      reviews: 200,
      description: "Navigate through exciting rapids and pristine waters in our professional rafting adventure, perfect for thrill-seekers and nature lovers alike.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🚣", name: "Rafting Equipment" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ],
      customerReviews: [
        {
          id: 1,
          name: "Chris Taylor",
          role: "Tourist",
          review: "Exciting rapids and beautiful scenery! The guides were experienced and made us feel safe.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 2,
          name: "Anna Lee",
          role: "Tourist",
          review: "Perfect rafting experience! The water was refreshing and the adventure was thrilling.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 3,
          name: "Robert Kim",
          role: "Tourist",
          review: "Great team building activity! Professional guides and well-maintained equipment.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 4,
          name: "Sophie Martin",
          role: "Tourist",
          review: "Amazing white water rafting experience! The rapids were exciting but manageable.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 5,
          name: "James Wilson",
          role: "Tourist",
          review: "Best rafting experience in Bali! Beautiful river and excellent service throughout.",
          avatar: "/src/assets/jeep-group.png"
        }
      ]
    },
    {
      id: 4,
      name: "ATV Ride",
      price: 150000,
      rating: 5,
      reviews: 180,
      description: "Explore rugged terrains and hidden trails with our ATV riding experience, combining adventure with stunning natural scenery.",
      image: "/src/assets/jeep-group.png",
      facilities: [
        { icon: "💧", name: "Water" },
        { icon: "🍽️", name: "Food" },
        { icon: "📸", name: "Photo Spot" },
        { icon: "🅿️", name: "Parking Area" },
        { icon: "🏍️", name: "ATV Vehicle" },
        { icon: "👨‍🏫", name: "Professional Guide" }
      ],
      gallery: [
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png",
        "/src/assets/jeep-group.png"
      ],
      customerReviews: [
        {
          id: 1,
          name: "Kevin Zhang",
          role: "Tourist",
          review: "Thrilling ATV adventure through amazing landscapes! The guides were helpful and the trails were exciting.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 2,
          name: "Rachel Green",
          role: "Tourist",
          review: "Perfect off-road experience! The ATV was well-maintained and the scenery was breathtaking.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 3,
          name: "Daniel Park",
          role: "Tourist",
          review: "Amazing adventure through hidden trails! Great for adrenaline junkies and nature lovers.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 4,
          name: "Michelle Wong",
          role: "Tourist",
          review: "Fantastic ATV experience with stunning views! Professional guides and excellent safety measures.",
          avatar: "/src/assets/jeep-group.png"
        },
        {
          id: 5,
          name: "Ryan Cooper",
          role: "Tourist",
          review: "Best ATV ride in Ubud! Challenging trails and beautiful nature. Highly recommended!",
          avatar: "/src/assets/jeep-group.png"
        }
      ]
    }
  ];

  // Auto-scroll reviews every 3 seconds
  useEffect(() => {
    if (selectedService && selectedService.customerReviews) {
      const interval = setInterval(() => {
        setCurrentReviewIndex((prevIndex) =>
          (prevIndex + 1) % selectedService.customerReviews.length
        );
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [selectedService]);

  const handleBookNow = (service) => {
    navigate('/booking', { state: { selectedService: service } });
  };

  const handleServiceClick = (service) => {
    setSelectedService(service);
    setCurrentReviewIndex(0); // Reset review index when selecting a service
  };

  const handleCloseDetail = () => {
    setSelectedService(null);
    setCurrentReviewIndex(0);
  };

  const handleImageClick = (image) => {
    setSelectedImage(image);
  };

  const handleCloseModal = () => {
    setSelectedImage(null);
  };

  if (selectedService) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-6 py-8">
          <button
            onClick={handleCloseDetail}
            className="mb-6 text-ubud-dark-green hover:text-ubud-light-green font-medium"
          >
            ← Back to Services
          </button>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="md:flex">
              <div className="md:w-1/2">
                <img
                  src={selectedService.image}
                  alt={selectedService.name}
                  className="w-full h-64 md:h-full object-cover"
                />
              </div>
              <div className="md:w-1/2 p-8">
                <h1 className="text-3xl font-bold text-ubud-dark-green mb-4">
                  {selectedService.name}
                </h1>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {selectedService.description}
                </p>
                <div className="mb-6">
                  <span className="text-gray-600">({selectedService.reviews} Reviews)</span>
                </div>
                <button
                  onClick={() => handleBookNow(selectedService)}
                  className="bg-ubud-dark-green text-white px-8 py-3 rounded-lg hover:bg-ubud-light-green transition-colors font-medium"
                >
                  Book Now
                </button>
              </div>
            </div>

            {/* Gallery */}
            <div className="p-8">
              <h3 className="text-xl font-semibold text-ubud-dark-green mb-4">Gallery</h3>
              <div className="grid grid-cols-6 gap-2 mb-8">
                {selectedService.gallery.map((img, index) => (
                  <img
                    key={index}
                    src={img}
                    alt={`${selectedService.name} ${index + 1}`}
                    className="w-full h-20 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => handleImageClick(img)}
                  />
                ))}
              </div>

              {/* Facilities */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-4">Facilities</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {selectedService.facilities.map((facility, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-2xl">{facility.icon}</span>
                      <span className="text-gray-700">{facility.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Customer Reviews */}
              <div>
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-6">Customer Review</h3>
                <div className="relative overflow-hidden">
                  <div
                    className="flex transition-transform duration-500 ease-in-out"
                    style={{ transform: `translateX(-${currentReviewIndex * 100}%)` }}
                  >
                    {selectedService.customerReviews.map((review, index) => (
                      <div key={review.id} className="w-full flex-shrink-0 px-2">
                        <div className={`p-6 rounded-lg ${
                          index % 3 === 0 ? 'bg-ubud-dark-green text-white' :
                          index % 3 === 1 ? 'bg-ubud-yellow text-ubud-dark-green' :
                          'bg-ubud-dark-green text-white'
                        }`}>
                          <div className="text-4xl mb-4">"</div>
                          <p className="mb-4">{review.review}</p>
                          <div className="flex items-center">
                            <img
                              src={review.avatar}
                              alt={review.name}
                              className="w-12 h-12 rounded-full mr-3"
                            />
                            <div>
                              <div className="font-semibold">{review.name}</div>
                              <div className="text-sm opacity-75">{review.role}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Review indicators */}
                  <div className="flex justify-center mt-4 space-x-2">
                    {selectedService.customerReviews.map((_, index) => (
                      <button
                        key={index}
                        className={`w-3 h-3 rounded-full transition-colors ${
                          index === currentReviewIndex
                            ? 'bg-ubud-dark-green'
                            : 'bg-gray-300'
                        }`}
                        onClick={() => setCurrentReviewIndex(index)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-12">
        <h1 className="text-4xl font-bold text-ubud-dark-green text-center mb-12">
          Our Services
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
          {services.map((service) => (
            <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <img
                src={service.image}
                alt={service.name}
                className="w-full h-48 object-cover cursor-pointer"
                onClick={() => handleServiceClick(service)}
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-ubud-dark-green mb-2">
                  {service.name}
                </h3>
                <div className="flex items-center mb-3">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(service.rating)].map((_, i) => (
                      <span key={i}>⭐</span>
                    ))}
                  </div>
                  <span className="text-gray-600 text-sm">({service.reviews} Reviews)</span>
                </div>
                <p className="text-gray-600 mb-4 text-sm line-clamp-3">
                  {service.description}
                </p>
                <div className="text-ubud-dark-green font-bold text-lg mb-4">
                  Rp. {service.price.toLocaleString('id-ID')}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleServiceClick(service)}
                    className="flex-1 bg-gray-100 text-ubud-dark-green py-2 px-4 rounded hover:bg-gray-200 transition-colors font-medium"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => handleBookNow(service)}
                    className="flex-1 bg-ubud-dark-green text-white py-2 px-4 rounded hover:bg-ubud-light-green transition-colors font-medium"
                  >
                    Book Now
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
