import React from "react";
import { Link } from "react-router-dom";

export default function Footer() {
  return (
    <footer className="bg-ubud-dark-green text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-4">
              <div className="text-2xl font-bold">
                <span className="text-ubud-yellow">Ubud</span> Activity
              </div>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              Ubud Activity Bali is a trusted activity service provider company
              located in Ubud area, Bali. We have been operating for more than 10 years.
              We offer adventure and cultural experiences in the beautiful Ubud area.
            </p>
            <div className="flex space-x-4">
              <div className="w-8 h-8 bg-ubud-yellow rounded-full flex items-center justify-center">
                <span className="text-ubud-dark-green font-bold">f</span>
              </div>
              <div className="w-8 h-8 bg-ubud-yellow rounded-full flex items-center justify-center">
                <span className="text-ubud-dark-green font-bold">@</span>
              </div>
              <div className="w-8 h-8 bg-ubud-yellow rounded-full flex items-center justify-center">
                <span className="text-ubud-dark-green font-bold">in</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-ubud-yellow font-bold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-ubud-yellow transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-gray-300 hover:text-ubud-yellow transition-colors">
                  Booking Experience
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-ubud-yellow transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/booking" className="text-gray-300 hover:text-ubud-yellow transition-colors">
                  Booking
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-ubud-yellow font-bold mb-4">Contact</h4>
            <div className="space-y-2 text-gray-300">
              <p>Ubud, Bali, Indonesia</p>
              <p>+62 123 456 789</p>
              <p><EMAIL></p>
            </div>
          </div>
        </div>

        <div className="border-t border-ubud-light-green mt-8 pt-8 text-center">
          <p className="text-gray-300">
            Copyright Ubud Activity. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
