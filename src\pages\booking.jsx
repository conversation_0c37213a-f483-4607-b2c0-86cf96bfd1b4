import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function Booking() {
  const location = useLocation();
  const navigate = useNavigate();
  const selectedService = location.state?.selectedService;

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    extraNo: '',
    date: '',
    guests: 3,
    mainActivity: selectedService?.name || '',
    otherActivity1: '',
    otherActivity2: ''
  });

  const [wantAdditionalActivities, setWantAdditionalActivities] = useState(false);

  const [selectedActivities, setSelectedActivities] = useState([
    selectedService || null,
    null,
    null,
    null
  ]);

  const services = [
    { id: 1, name: "Trekking and Jeep", price: 150000 },
    { id: 2, name: "Swing and Nest", price: 150000 },
    { id: 3, name: "Rafting", price: 150000 },
    { id: 4, name: "ATV Ride", price: 150000 }
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleActivityChange = (index, activityName) => {
    const activity = services.find(s => s.name === activityName);
    const newSelectedActivities = [...selectedActivities];
    newSelectedActivities[index] = activity;
    setSelectedActivities(newSelectedActivities);

    if (index === 0) {
      setFormData({ ...formData, mainActivity: activityName });
    } else if (index === 1) {
      setFormData({ ...formData, otherActivity1: activityName });
    } else if (index === 2) {
      setFormData({ ...formData, otherActivity2: activityName });
    }
  };

  const calculateTotal = () => {
    return selectedActivities
      .filter(activity => activity !== null)
      .reduce((total, activity) => total + activity.price, 0);
  };

  const handleMakeBook = () => {
    if (!formData.name || !formData.phone || !formData.date || !formData.mainActivity) {
      alert('Please fill in all required fields');
      return;
    }

    const bookingData = {
      ...formData,
      selectedActivities: selectedActivities.filter(a => a !== null),
      total: calculateTotal(),
      bookingDate: new Date().toISOString()
    };

    navigate('/payment', { state: { bookingData } });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-ubud-light-green to-ubud-dark-green">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-5xl mx-auto">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden">
            <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
              <h1 className="text-3xl font-bold text-white text-center">Book Your Adventure</h1>
              <p className="text-ubud-cream text-center mt-2">Fill in the details below to book your exciting activities</p>
            </div>

            <div className="p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Side - Form */}
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-ubud-dark-green font-semibold mb-2">Full Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="Enter your name"
                        className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-ubud-dark-green font-semibold mb-2">Number of People *</label>
                      <div className="flex items-center">
                        <input
                          type="number"
                          name="guests"
                          value={formData.guests}
                          onChange={handleChange}
                          min="1"
                          className="w-20 px-4 py-3 rounded-l-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        />
                        <span className="bg-ubud-dark-green text-white px-4 py-3 rounded-r-lg font-medium">
                          People
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-ubud-dark-green font-semibold mb-2">Phone Number *</label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="08123456789"
                        className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-ubud-dark-green font-semibold mb-2">Booking Date *</label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-ubud-dark-green font-semibold mb-2">Main Activity *</label>
                    <select
                      name="mainActivity"
                      value={formData.mainActivity}
                      onChange={(e) => {
                        handleChange(e);
                        handleActivityChange(0, e.target.value);
                      }}
                      className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                      required
                    >
                      <option value="">Select Main Activity</option>
                      {services.map((service) => (
                        <option key={service.id} value={service.name}>
                          {service.name} - Rp. {service.price.toLocaleString('id-ID')}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Additional Activities Question */}
                  <div className="bg-gray-50 p-4 rounded-lg border-2 border-gray-200">
                    <label className="block text-ubud-dark-green font-semibold mb-3">
                      Do you want to book additional activities at the same time?
                    </label>
                    <div className="flex items-center space-x-6">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="additionalActivities"
                          value="yes"
                          checked={wantAdditionalActivities === true}
                          onChange={() => setWantAdditionalActivities(true)}
                          className="mr-2 text-ubud-dark-green focus:ring-ubud-dark-green"
                        />
                        <span className="text-ubud-dark-green font-medium">Yes</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          name="additionalActivities"
                          value="no"
                          checked={wantAdditionalActivities === false}
                          onChange={() => {
                            setWantAdditionalActivities(false);
                            // Clear additional activities when user selects "No"
                            setFormData({
                              ...formData,
                              otherActivity1: '',
                              otherActivity2: ''
                            });
                            // Clear from selectedActivities array
                            const newSelectedActivities = [...selectedActivities];
                            newSelectedActivities[1] = null;
                            newSelectedActivities[2] = null;
                            setSelectedActivities(newSelectedActivities);
                          }}
                          className="mr-2 text-ubud-dark-green focus:ring-ubud-dark-green"
                        />
                        <span className="text-ubud-dark-green font-medium">No</span>
                      </label>
                    </div>
                  </div>

                  {/* Additional Activities Selection - Only show if user wants additional activities */}
                  {wantAdditionalActivities && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-ubud-dark-green font-semibold mb-2">Additional Activity 1</label>
                        <select
                          name="otherActivity1"
                          value={formData.otherActivity1}
                          onChange={(e) => {
                            handleChange(e);
                            handleActivityChange(1, e.target.value);
                          }}
                          className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        >
                          <option value="">Choose Additional Activity (Optional)</option>
                          {services.map((service) => (
                            <option key={service.id} value={service.name}>
                              {service.name} - Rp. {service.price.toLocaleString('id-ID')}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-ubud-dark-green font-semibold mb-2">Additional Activity 2</label>
                        <select
                          name="otherActivity2"
                          value={formData.otherActivity2}
                          onChange={(e) => {
                            handleChange(e);
                            handleActivityChange(2, e.target.value);
                          }}
                          className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                        >
                          <option value="">Choose Additional Activity (Optional)</option>
                          {services.map((service) => (
                            <option key={service.id} value={service.name}>
                              {service.name} - Rp. {service.price.toLocaleString('id-ID')}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Side - Summary */}
                <div className="bg-gradient-to-br from-ubud-dark-green to-ubud-light-green rounded-xl p-6 text-white h-fit">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <span className="mr-2">🛒</span>
                    Booking Summary
                  </h3>

                  <div className="space-y-3 mb-6">
                    {selectedActivities.filter(activity => activity !== null).map((activity, index) => (
                      <div key={index} className="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span className="text-sm">{activity.name}</span>
                        <span className="font-semibold">Rp. {activity.price.toLocaleString('id-ID')}</span>
                      </div>
                    ))}
                  </div>

                  <div className="border-t border-white/20 pt-4 mb-6">
                    <div className="flex justify-between items-center text-lg font-bold">
                      <span>Total Amount</span>
                      <span className="text-ubud-yellow">Rp. {calculateTotal().toLocaleString('id-ID')}</span>
                    </div>
                    <div className="text-sm text-ubud-cream mt-1">
                      For {formData.guests} people
                    </div>
                  </div>

                  <button
                    onClick={handleMakeBook}
                    className="w-full bg-ubud-yellow text-ubud-dark-green py-4 rounded-lg font-bold text-lg hover:bg-yellow-400 transition-colors shadow-lg"
                  >
                    Proceed to Payment
                  </button>

                  <div className="mt-4 text-xs text-ubud-cream text-center">
                    <p>✓ Secure booking process</p>
                    <p>✓ Instant confirmation</p>
                    <p>✓ 24/7 customer support</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
