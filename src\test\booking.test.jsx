import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Booking from '../pages/booking.jsx';

// Mock useNavigate and useLocation
const mockNavigate = jest.fn();
const mockLocation = {
  state: {
    selectedService: { id: 1, name: "ATV Ride", price: 150000 }
  }
};

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

const BookingWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Booking Component', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  test('renders booking form with main activity', () => {
    render(
      <BookingWrapper>
        <Booking />
      </BookingWrapper>
    );

    expect(screen.getByText('Book Your Adventure')).toBeInTheDocument();
    expect(screen.getByLabelText(/Main Activity/)).toBeInTheDocument();
  });

  test('shows additional activities question', () => {
    render(
      <BookingWrapper>
        <Booking />
      </BookingWrapper>
    );

    expect(screen.getByText('Do you want to book additional activities at the same time?')).toBeInTheDocument();
    expect(screen.getByLabelText('Yes')).toBeInTheDocument();
    expect(screen.getByLabelText('No')).toBeInTheDocument();
  });

  test('shows additional activity dropdowns when Yes is selected', () => {
    render(
      <BookingWrapper>
        <Booking />
      </BookingWrapper>
    );

    // Initially, additional activities should not be visible
    expect(screen.queryByText('Additional Activity 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Additional Activity 2')).not.toBeInTheDocument();

    // Click Yes
    fireEvent.click(screen.getByLabelText('Yes'));

    // Now additional activities should be visible
    expect(screen.getByText('Additional Activity 1')).toBeInTheDocument();
    expect(screen.getByText('Additional Activity 2')).toBeInTheDocument();
  });

  test('hides additional activity dropdowns when No is selected', () => {
    render(
      <BookingWrapper>
        <Booking />
      </BookingWrapper>
    );

    // Click Yes first to show additional activities
    fireEvent.click(screen.getByLabelText('Yes'));
    expect(screen.getByText('Additional Activity 1')).toBeInTheDocument();

    // Click No to hide additional activities
    fireEvent.click(screen.getByLabelText('No'));
    expect(screen.queryByText('Additional Activity 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Additional Activity 2')).not.toBeInTheDocument();
  });

  test('clears additional activities when switching from Yes to No', () => {
    render(
      <BookingWrapper>
        <Booking />
      </BookingWrapper>
    );

    // Click Yes to show additional activities
    fireEvent.click(screen.getByLabelText('Yes'));
    
    // Select an additional activity (this would require more complex mocking)
    // For now, we just test that the No option clears the selection
    fireEvent.click(screen.getByLabelText('No'));
    
    // Additional activities should be hidden
    expect(screen.queryByText('Additional Activity 1')).not.toBeInTheDocument();
  });
});
