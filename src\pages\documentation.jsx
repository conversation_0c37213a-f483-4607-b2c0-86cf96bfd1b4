import React, { useState } from "react";

export default function Documentation() {
  const [selectedFilter, setSelectedFilter] = useState('all');

  const documentationData = [
    {
      id: 1,
      title: "Trekking Adventure",
      category: "trekking",
      image: "https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Experience breathtaking mountain trails and scenic views"
    },
    {
      id: 2,
      title: "Jeep Safari",
      category: "jeep",
      image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Off-road adventure through Ubud's hidden gems"
    },
    {
      id: 3,
      title: "Jungle Swing",
      category: "swing",
      image: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Swing over the lush jungle canopy"
    },
    {
      id: 4,
      title: "Bird's Nest Experience",
      category: "nest",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Relax in our unique bird's nest overlooking the valley"
    },
    {
      id: 5,
      title: "White Water Rafting",
      category: "rafting",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Navigate thrilling rapids through tropical landscapes"
    },
    {
      id: 6,
      title: "ATV Adventure",
      category: "atv",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Explore rugged terrain on all-terrain vehicles"
    },
    {
      id: 7,
      title: "Cultural Village Tour",
      category: "cultural",
      image: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Immerse yourself in traditional Balinese culture"
    },
    {
      id: 8,
      title: "Rice Terrace Trekking",
      category: "trekking",
      image: "https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Walk through stunning terraced rice fields"
    },
    {
      id: 9,
      title: "Waterfall Adventure",
      category: "trekking",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Discover hidden waterfalls in the jungle"
    },
    {
      id: 10,
      title: "Sunrise Jeep Tour",
      category: "jeep",
      image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Watch the sunrise from mountain peaks"
    },
    {
      id: 11,
      title: "River Rafting Challenge",
      category: "rafting",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "Advanced rafting for thrill seekers"
    },
    {
      id: 12,
      title: "Extreme Swing",
      category: "swing",
      image: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      description: "The ultimate adrenaline swing experience"
    }
  ];

  const categories = [
    { id: 'all', name: 'All Activities', icon: '🌟' },
    { id: 'trekking', name: 'Trekking', icon: '🥾' },
    { id: 'jeep', name: 'Jeep Safari', icon: '🚙' },
    { id: 'swing', name: 'Swing', icon: '🌿' },
    { id: 'nest', name: 'Bird Nest', icon: '🪺' },
    { id: 'rafting', name: 'Rafting', icon: '🚣' },
    { id: 'atv', name: 'ATV', icon: '🏍️' },
    { id: 'cultural', name: 'Cultural', icon: '🏛️' }
  ];

  const filteredDocumentation = selectedFilter === 'all' 
    ? documentationData 
    : documentationData.filter(item => item.category === selectedFilter);

  return (
    <div className="min-h-screen bg-gradient-to-br from-ubud-cream to-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green py-16">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Service Documentation
          </h1>
          <p className="text-ubud-cream text-lg max-w-2xl mx-auto">
            Explore our comprehensive gallery showcasing the amazing adventures and experiences we offer in beautiful Ubud, Bali
          </p>
        </div>
      </div>

      {/* Filter Section */}
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <h3 className="text-xl font-bold text-ubud-dark-green mb-4 text-center">
            Filter by Activity Type
          </h3>
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedFilter(category.id)}
                className={`px-4 py-2 rounded-full font-medium transition-all flex items-center space-x-2 ${
                  selectedFilter === category.id
                    ? 'bg-ubud-dark-green text-white shadow-lg'
                    : 'bg-gray-100 text-ubud-dark-green hover:bg-ubud-light-green hover:text-white'
                }`}
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Documentation Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredDocumentation.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute top-4 right-4">
                  <span className="bg-ubud-yellow text-ubud-dark-green px-2 py-1 rounded-full text-xs font-bold">
                    {categories.find(cat => cat.id === item.category)?.icon}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-lg font-bold text-ubud-dark-green mb-2">
                  {item.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Results Count */}
        <div className="text-center mt-8">
          <p className="text-ubud-dark-green font-medium">
            Showing {filteredDocumentation.length} of {documentationData.length} activities
          </p>
        </div>
      </div>
    </div>
  );
}
